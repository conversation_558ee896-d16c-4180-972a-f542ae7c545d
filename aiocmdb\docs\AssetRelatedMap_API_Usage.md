# 资产关联映射 API 使用说明

## 概述

本文档描述了资产关联映射功能的新增特性，包括反向关联功能和反向查询功能。

## 数据库表结构

### AssetsRelated 表结构
```sql
desc AssetsRelated;
+---------------------+----------------+------+-------+---------+-------+
| Field               | Type           | Null | Key   | Default | Extra |
+---------------------+----------------+------+-------+---------+-------+
| id                  | bigint         | NO   | true  | NULL    |       |
| flumeTimestamp      | varchar(65533) | YES  | false | NULL    |       |
| collectedTime       | datetime       | YES  | false | NULL    |       |
| eid                 | bigint         | YES  | false | NULL    |       |
| primaryModelCode    | varchar(65533) | YES  | false | NULL    |       |
| primarySinkName     | varchar(65533) | YES  | false | NULL    |       |
| primaryAssetId      | bigint         | YES  | false | NULL    |       |
| associatedModelCode | varchar(65533) | YES  | false | NULL    |       |
| associatedSinkName  | varchar(65533) | YES  | false | NULL    |       |
| associatedAssetId   | bigint         | YES  | false | NULL    |       |
+---------------------+----------------+------+-------+---------+-------+
```

## API 端点

### 1. 新增资产关联（支持反向关联）

**端点**: `POST /asset/maintenance/v2/map/assets`

**功能**: 保存主资产的对应关联资产，支持反向关联功能

**请求体示例**:
```json
[
  {
    "eid": "1001",
    "primaryModelCode": "SERVER",
    "primarySinkName": "ServerAssets",
    "primaryAssetId": 12345,
    "associatedModelCode": "NETWORK",
    "associatedSinkName": "NetworkAssets", 
    "associatedAssetId": 67890,
    "reverseRelatedAssets": [
      {
        "beAssociatedModelCode": "DATABASE",
        "beAssociatedSinkName": "DatabaseAssets",
        "beAssociatedAssetId": 11111,
        "currentAssetModelCode": "APPLICATION",
        "currentAssetSinkName": "ApplicationAssets",
        "currentAssetId": 22222
      },
      {
        "beAssociatedModelCode": "STORAGE",
        "beAssociatedSinkName": "StorageAssets", 
        "beAssociatedAssetId": 33333,
        "currentAssetModelCode": "BACKUP",
        "currentAssetSinkName": "BackupAssets",
        "currentAssetId": 44444
      }
    ]
  }
]
```

**说明**:
- 正向关联：`primaryModelCode/primarySinkName/primaryAssetId` -> `associatedModelCode/associatedSinkName/associatedAssetId`
- 反向关联：`beAssociatedModelCode/beAssociatedSinkName/beAssociatedAssetId` -> `currentAssetModelCode/currentAssetSinkName/currentAssetId`
- 反向关联数据保存时，`be`开头的字段作为`primary`字段，`current`开头的字段作为`associated`字段

### 2. 查询资产关联

**端点**: `GET /asset/maintenance/v2/map/assets`

**参数**:
- `eid`: 租户ID
- `primaryAssetId`: 主资产ID

**功能**: 根据主资产ID查询其关联的资产

### 3. 反向查询资产关联（新增）

**端点**: `GET /asset/maintenance/v2/map/assets/reverse`

**参数**:
- `eid`: 租户ID  
- `associatedAssetId`: 关联资产ID

**功能**: 根据关联资产ID反向查询主资产

**示例请求**:
```
GET /asset/maintenance/v2/map/assets/reverse?eid=1001&associatedAssetId=67890
```

## 业务逻辑说明

### 正向关联保存逻辑
1. 接收 `AssetRelatedMap` 列表
2. 为每个关联关系生成唯一ID
3. 保存到 `AssetsRelated` 表

### 反向关联保存逻辑  
1. 处理 `reverseRelatedAssets` 数组中的每个元素
2. 将 `beAssociatedModelCode/beAssociatedSinkName/beAssociatedAssetId` 作为 `primaryModelCode/primarySinkName/primaryAssetId`
3. 将 `currentAssetModelCode/currentAssetSinkName/currentAssetId` 作为 `associatedModelCode/associatedSinkName/associatedAssetId`
4. 生成新的关联记录保存到数据库

### 反向查询逻辑
1. 根据 `associatedAssetId` 查询 `AssetsRelated` 表
2. 获取对应的 `primaryAssetId` 信息
3. 按照原有查询逻辑组织返回数据结构

## 响应格式

查询接口返回 `AssetRelatedCategoryClassificationTree` 结构，包含分类和分组的详细信息。

## 注意事项

1. 反向关联是可选的，如果不需要可以不传 `reverseRelatedAssets` 字段
2. 反向关联会在数据库中创建额外的关联记录
3. 反向查询使用与正向查询相同的数据结构和逻辑
4. 所有操作都会自动更新 `collectedTime` 和 `flumeTimestamp` 字段
