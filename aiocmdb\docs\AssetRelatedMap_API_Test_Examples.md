# 资产关联映射 API 测试示例

## 1. 新增资产关联（包含反向关联）

### 请求示例

```bash
curl -X POST "http://localhost:8080/asset/maintenance/v2/map/assets" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "eid": "1001",
      "primaryModelCode": "SERVER",
      "primarySinkName": "ServerAssets",
      "primaryAssetId": 12345,
      "associatedModelCode": "NETWORK",
      "associatedSinkName": "NetworkAssets",
      "associatedAssetId": 67890,
      "reverseRelatedAssets": [
        {
          "beAssociatedModelCode": "DATABASE",
          "beAssociatedSinkName": "DatabaseAssets",
          "beAssociatedAssetId": 11111,
          "currentAssetModelCode": "APPLICATION",
          "currentAssetSinkName": "ApplicationAssets",
          "currentAssetId": 22222
        },
        {
          "beAssociatedModelCode": "STORAGE",
          "beAssociatedSinkName": "StorageAssets",
          "beAssociatedAssetId": 33333,
          "currentAssetModelCode": "BACKUP",
          "currentAssetSinkName": "BackupAssets",
          "currentAssetId": 44444
        }
      ]
    }
  ]'
```

### 预期结果

该请求会在数据库中创建3条记录：

1. **正向关联记录**:
   - primaryModelCode: "SERVER"
   - primarySinkName: "ServerAssets"
   - primaryAssetId: 12345
   - associatedModelCode: "NETWORK"
   - associatedSinkName: "NetworkAssets"
   - associatedAssetId: 67890

2. **反向关联记录1**:
   - primaryModelCode: "DATABASE"
   - primarySinkName: "DatabaseAssets"
   - primaryAssetId: 11111
   - associatedModelCode: "APPLICATION"
   - associatedSinkName: "ApplicationAssets"
   - associatedAssetId: 22222

3. **反向关联记录2**:
   - primaryModelCode: "STORAGE"
   - primarySinkName: "StorageAssets"
   - primaryAssetId: 33333
   - associatedModelCode: "BACKUP"
   - associatedSinkName: "BackupAssets"
   - associatedAssetId: 44444

## 2. 查询资产关联（正向查询）

### 请求示例

```bash
curl -X GET "http://localhost:8080/asset/maintenance/v2/map/assets?eid=1001&primaryAssetId=12345"
```

### 功能说明
- 根据主资产ID (12345) 查询其关联的资产
- 返回与该主资产相关联的所有资产信息

## 3. 反向查询资产关联

### 请求示例

```bash
curl -X GET "http://localhost:8080/asset/maintenance/v2/map/assets/reverse?eid=1001&associatedAssetId=67890"
```

### 功能说明
- 根据关联资产ID (67890) 反向查询主资产
- 返回以该资产作为关联资产的所有主资产信息

## 4. 测试场景

### 场景1：只有正向关联
```json
[
  {
    "eid": "1001",
    "primaryModelCode": "SERVER",
    "primarySinkName": "ServerAssets",
    "primaryAssetId": 12345,
    "associatedModelCode": "NETWORK",
    "associatedSinkName": "NetworkAssets",
    "associatedAssetId": 67890
  }
]
```

### 场景2：包含反向关联
```json
[
  {
    "eid": "1001",
    "primaryModelCode": "SERVER",
    "primarySinkName": "ServerAssets",
    "primaryAssetId": 12345,
    "associatedModelCode": "NETWORK",
    "associatedSinkName": "NetworkAssets",
    "associatedAssetId": 67890,
    "reverseRelatedAssets": [
      {
        "beAssociatedModelCode": "DATABASE",
        "beAssociatedSinkName": "DatabaseAssets",
        "beAssociatedAssetId": 11111,
        "currentAssetModelCode": "APPLICATION",
        "currentAssetSinkName": "ApplicationAssets",
        "currentAssetId": 22222
      }
    ]
  }
]
```

### 场景3：多个反向关联
```json
[
  {
    "eid": "1001",
    "primaryModelCode": "SERVER",
    "primarySinkName": "ServerAssets",
    "primaryAssetId": 12345,
    "associatedModelCode": "NETWORK",
    "associatedSinkName": "NetworkAssets",
    "associatedAssetId": 67890,
    "reverseRelatedAssets": [
      {
        "beAssociatedModelCode": "DATABASE",
        "beAssociatedSinkName": "DatabaseAssets",
        "beAssociatedAssetId": 11111,
        "currentAssetModelCode": "APPLICATION",
        "currentAssetSinkName": "ApplicationAssets",
        "currentAssetId": 22222
      },
      {
        "beAssociatedModelCode": "STORAGE",
        "beAssociatedSinkName": "StorageAssets",
        "beAssociatedAssetId": 33333,
        "currentAssetModelCode": "BACKUP",
        "currentAssetSinkName": "BackupAssets",
        "currentAssetId": 44444
      }
    ]
  }
]
```

## 5. 验证步骤

1. **发送新增请求**后，检查数据库中的记录数量
2. **执行正向查询**，验证能否正确查询到关联资产
3. **执行反向查询**，验证能否正确查询到主资产
4. **检查数据一致性**，确保反向关联数据正确保存

## 6. 注意事项

- 所有ID字段都使用长整型 (long)
- eid 字段为字符串类型
- 反向关联是可选的，可以为空或不传
- 每次保存都会自动更新时间戳字段
